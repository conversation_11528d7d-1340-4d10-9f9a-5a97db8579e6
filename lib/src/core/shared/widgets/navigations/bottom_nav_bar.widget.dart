import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:dropx/src/core/shared/extensions/context_extensions.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../theme/color_manager.dart';
import 'controller/bottom_nav_bar.controller.dart';

class BottomNavBarWidget extends ConsumerWidget {
  const BottomNavBarWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentIndex = ref.watch(bottomNavigationControllerProvider);
    final bottomNavCtrl = ref.read(bottomNavControllerProvider);

    final iconSize = 24.w;

    return Container(
      margin: EdgeInsets.all(16.w),
      padding: EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            spreadRadius: 0,
            blurRadius: 10,
            offset: const Offset(0, -1),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildNavItem(
            context: context,
            icon: CupertinoIcons.house_fill,
            label: context.tr.homePage,
            index: 0,
            currentIndex: currentIndex,
            iconSize: iconSize,
            onTap: () => bottomNavCtrl.changeIndex(0),
          ),
          _buildNavItem(
            context: context,
            icon: Icons.history,
            label: context.tr.shipmentsHistory,
            index: 1,
            currentIndex: currentIndex,
            iconSize: iconSize,
            onTap: () => bottomNavCtrl.changeIndex(1),
          ),
          _buildNavItem(
            context: context,
            icon: Icons.account_balance_wallet,
            label: context.tr.wallet,
            index: 2,
            currentIndex: currentIndex,
            iconSize: iconSize,
            onTap: () => bottomNavCtrl.changeIndex(2),
          ),
          _buildNavItem(
            context: context,
            icon: Icons.menu,
            label: context.tr.menu,
            index: 3,
            currentIndex: currentIndex,
            iconSize: iconSize,
            onTap: () => bottomNavCtrl.changeIndex(3),
          ),
        ],
      ),
    );
  }

  Widget _buildNavItem({
    required BuildContext context,
    required IconData icon,
    required String label,
    required int index,
    required int currentIndex,
    required double iconSize,
    required VoidCallback onTap,
  }) {
    final isSelected = currentIndex == index;

    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: isSelected ? 16.w : 8.w,
          vertical: 8.h,
        ),
        decoration: BoxDecoration(
          border: isSelected
              ? Border.all(color: ColorManager.primaryColor, width: 2)
              : null,
          borderRadius: BorderRadius.circular(100.r),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: iconSize,
              color: isSelected
                  ? ColorManager.primaryColor
                  : ColorManager.greyIcon,
            ),
            if (isSelected) ...[
              AppGaps.gap8,
              Text(
                label,
                style: AppTextStyles.bodySmall.copyWith(
                  color: ColorManager.primaryColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
