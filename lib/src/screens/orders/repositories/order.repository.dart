import 'package:dropx/src/core/consts/network/api_endpoints.dart';
import 'package:dropx/src/screens/orders/models/orders_response.model.dart';
import 'package:xr_helper/xr_helper.dart';

class OrdersRepository with BaseRepository {
  final BaseApiServices networkApiService;

  OrdersRepository({
    required this.networkApiService,
  });

  // * Get Orders
  Future<OrdersResponseModel> getOrders() async {
    return baseFunction(
      () async {
        const url = ApiEndpoints.orders;

        final response = await networkApiService.getResponse(url);

        final ordersResponse = OrdersResponseModel.fromJson(response);

        return ordersResponse;
      },
    );
  }

  // * Create Order
  Future<void> createOrder({
    required Map<String, dynamic> data,
  }) async {
    return baseFunction(
      () async {
        const url = ApiEndpoints.orders;

        await networkApiService.postResponse(
          url,
          body: data,
        );
      },
    );
  }
}
