import 'package:dropx/src/screens/orders/models/orders_response.model.dart';
import 'package:dropx/src/screens/orders/repositories/order.repository.dart';
import 'package:xr_helper/xr_helper.dart';

class OrdersController extends BaseVM {
  final OrdersRepository ordersRepo;

  OrdersController({
    required this.ordersRepo,
  });

  // * Get Orders
  Future<OrdersResponseModel> getOrders() async {
    return await baseFunction(
      () async {
        return await ordersRepo.getOrders();
      },
    );
  }

  // * Create Order
  Future<void> createOrder({
    required Map<String, dynamic> data,
  }) async {
    return await baseFunction(
      () async {
        return await ordersRepo.createOrder(data: data);
      },
    );
  }
}
