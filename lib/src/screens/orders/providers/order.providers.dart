import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:dropx/src/core/shared/providers/network_api_service_provider.dart';
import 'package:dropx/src/screens/orders/controllers/order.controller.dart';
import 'package:dropx/src/screens/orders/repositories/order.repository.dart';

// * Orders Repo Provider ========================================
final ordersRepoProvider = Provider<OrdersRepository>((ref) {
  final networkApiService = ref.watch(networkServiceProvider);

  return OrdersRepository(networkApiService: networkApiService);
});

// * Orders Change Notifier Provider ========================================
final ordersControllerNotifierProvider =
    ChangeNotifierProvider<OrdersController>(
  (ref) {
    final ordersRepo = ref.watch(ordersRepoProvider);

    return OrdersController(
      ordersRepo: ordersRepo,
    );
  },
);

// * Orders Provider ========================================
final ordersControllerProvider = Provider<OrdersController>(
  (ref) {
    final ordersRepo = ref.watch(ordersRepoProvider);

    return OrdersController(
      ordersRepo: ordersRepo,
    );
  },
);

// * Get Orders Future Provider ========================================
final getOrdersFutureProvider = FutureProvider(
  (ref) {
    final ordersController = ref.watch(ordersControllerProvider);

    return ordersController.getOrders();
  },
);
