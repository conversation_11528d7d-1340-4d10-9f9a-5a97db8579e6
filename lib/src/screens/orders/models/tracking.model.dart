class TrackingModel {
  final int id;
  final String status;
  final String? statusText;
  final String note;
  final String user;
  final String? station;
  final DateTime createdAt;

  TrackingModel({
    required this.id,
    required this.status,
    this.statusText,
    required this.note,
    required this.user,
    this.station,
    required this.createdAt,
  });

  factory TrackingModel.fromJson(Map<String, dynamic> json) {
    return TrackingModel(
      id: json['id'] as int,
      status: json['status'] as String,
      statusText: json['status_text'] as String?,
      note: json['note'] as String,
      user: json['user'] as String,
      station: json['station'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'status': status,
      'status_text': statusText,
      'note': note,
      'user': user,
      'station': station,
      'created_at': createdAt.toIso8601String(),
    };
  }

  TrackingModel copyWith({
    int? id,
    String? status,
    String? statusText,
    String? note,
    String? user,
    String? station,
    DateTime? createdAt,
  }) {
    return TrackingModel(
      id: id ?? this.id,
      status: status ?? this.status,
      statusText: statusText ?? this.statusText,
      note: note ?? this.note,
      user: user ?? this.user,
      station: station ?? this.station,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TrackingModel &&
        other.id == id &&
        other.status == status &&
        other.statusText == statusText &&
        other.note == note &&
        other.user == user &&
        other.station == station &&
        other.createdAt == createdAt;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      status,
      statusText,
      note,
      user,
      station,
      createdAt,
    );
  }

  @override
  String toString() {
    return 'TrackingModel(id: $id, status: $status, statusText: $statusText, note: $note, user: $user, station: $station, createdAt: $createdAt)';
  }
}
