import 'tracking.model.dart';

class OrderModel {
  final int id;
  final String fromStation;
  final String toStation;
  final String type;
  final String receiverName;
  final String receiverPhone;
  final String note;
  final String price;
  final String status;
  final String? statusText;
  final DateTime createdAt;
  final DateTime updatedAt;
  final TrackingModel? latestTracking;

  OrderModel({
    required this.id,
    required this.fromStation,
    required this.toStation,
    required this.type,
    required this.receiverName,
    required this.receiverPhone,
    required this.note,
    required this.price,
    required this.status,
    this.statusText,
    required this.createdAt,
    required this.updatedAt,
    this.latestTracking,
  });

  factory OrderModel.fromJson(Map<String, dynamic> json) {
    return OrderModel(
      id: json['id'] as int,
      fromStation: json['from_station'] as String,
      toStation: json['to_station'] as String,
      type: json['type'] as String,
      receiverName: json['receiver_name'] as String,
      receiverPhone: json['receiver_phone'] as String,
      note: json['note'] as String,
      price: json['price'] as String,
      status: json['status'] as String,
      statusText: json['status_text'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      latestTracking: json['latest_tracking'] != null
          ? TrackingModel.fromJson(json['latest_tracking'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'from_station': fromStation,
      'to_station': toStation,
      'type': type,
      'receiver_name': receiverName,
      'receiver_phone': receiverPhone,
      'note': note,
      'price': price,
      'status': status,
      'status_text': statusText,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'latest_tracking': latestTracking?.toJson(),
    };
  }

  OrderModel copyWith({
    int? id,
    String? fromStation,
    String? toStation,
    String? type,
    String? receiverName,
    String? receiverPhone,
    String? note,
    String? price,
    String? status,
    String? statusText,
    DateTime? createdAt,
    DateTime? updatedAt,
    TrackingModel? latestTracking,
  }) {
    return OrderModel(
      id: id ?? this.id,
      fromStation: fromStation ?? this.fromStation,
      toStation: toStation ?? this.toStation,
      type: type ?? this.type,
      receiverName: receiverName ?? this.receiverName,
      receiverPhone: receiverPhone ?? this.receiverPhone,
      note: note ?? this.note,
      price: price ?? this.price,
      status: status ?? this.status,
      statusText: statusText ?? this.statusText,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      latestTracking: latestTracking ?? this.latestTracking,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is OrderModel &&
        other.id == id &&
        other.fromStation == fromStation &&
        other.toStation == toStation &&
        other.type == type &&
        other.receiverName == receiverName &&
        other.receiverPhone == receiverPhone &&
        other.note == note &&
        other.price == price &&
        other.status == status &&
        other.statusText == statusText &&
        other.createdAt == createdAt &&
        other.updatedAt == updatedAt &&
        other.latestTracking == latestTracking;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      fromStation,
      toStation,
      type,
      receiverName,
      receiverPhone,
      note,
      price,
      status,
      statusText,
      createdAt,
      updatedAt,
      latestTracking,
    );
  }

  @override
  String toString() {
    return 'OrderModel(id: $id, fromStation: $fromStation, toStation: $toStation, type: $type, receiverName: $receiverName, receiverPhone: $receiverPhone, note: $note, price: $price, status: $status, statusText: $statusText, createdAt: $createdAt, updatedAt: $updatedAt, latestTracking: $latestTracking)';
  }
}
