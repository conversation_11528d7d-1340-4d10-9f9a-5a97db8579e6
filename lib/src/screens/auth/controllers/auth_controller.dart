import 'package:dropx/src/core/consts/network/api_strings.dart';
import 'package:dropx/src/screens/auth/models/user_model.dart';
import 'package:dropx/src/screens/auth/repositories/auth_repository.dart';
import 'package:dropx/src/screens/auth/view/login/login.screen.dart';
import 'package:dropx/src/screens/auth/view/verification/verification.screen.dart';
import 'package:dropx/src/screens/main_screen/view/main.screen.dart';
import 'package:xr_helper/xr_helper.dart';

class AuthController extends BaseVM {
  final AuthRepository authRepo;

  AuthController({
    required this.authRepo,
  });

  // * Login
  Future<bool> login({
    required Map<String, dynamic> data,
  }) async {
    return await baseFunction(
      () async {
        final user = await _setLoginUser(data);

        final userData = await authRepo.login(user: user);

        return userData;
      },
      additionalFunction: () {
        const MainScreen().navigate;
      },
    );
  }

  // * Register
  Future<Map<String, dynamic>> register({
    required Map<String, dynamic> data,
  }) async {
    return await baseFunction(
      () async {
        final user = await _setRegisterUser(data);

        final response = await authRepo.register(user: user);

        return response;
      },
      additionalFunction: () {
        VerificationScreen(
          phone: data[FieldsConsts.mobile],
          userData: data,
        ).navigate;
      },
    );
  }

  // * Verify Code
  Future<bool> verifyCode({
    required Map<String, dynamic> userData,
    required String code,
  }) async {
    return await baseFunction(
      () async {
        final user = await _setRegisterUser(userData);

        final result = await authRepo.verifyCode(user: user, code: code);

        return result;
      },
      additionalFunction: () {
        const MainScreen().navigate;
      },
    );
  }

  // * Set Login User
  Future<UserModel> _setLoginUser(
    Map<String, dynamic> data,
  ) async {
    final user = UserModel(
      phone: data[FieldsConsts.mobile],
      password: data[FieldsConsts.password],
    );

    return user;
  }

  // * Set Register User
  Future<UserModel> _setRegisterUser(
    Map<String, dynamic> data,
  ) async {
    final user = UserModel(
      name: data[FieldsConsts.name],
      phone: data[FieldsConsts.mobile],
      password: data[FieldsConsts.password],
      nationalId: data[FieldsConsts.idNumber],
    );

    return user;
  }

  // * Logout
  Future<void> logout() async {
    return await baseFunction(
      () async {
        await authRepo.logout();

        const LoginScreen().navigateReplacement;
      },
    );
  }

// * Get Countries
// Future<List<CountryModel>> getCountries() async {
//   return await baseFunction(
//     () async {
//       return await authRepo.getCountries();
//     },
//   );
// }
}
