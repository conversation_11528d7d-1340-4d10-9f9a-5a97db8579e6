import 'package:dropx/src/core/shared/extensions/riverpod_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:dropx/src/core/shared/extensions/context_extensions.dart';
import 'package:dropx/src/core/shared/widgets/lists/base_list.dart';
import 'package:dropx/src/core/theme/color_manager.dart';
import 'package:dropx/src/screens/orders/providers/order.providers.dart';
import 'package:dropx/src/screens/orders/models/order.model.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:xr_helper/xr_helper.dart';

import '../widgets/shipment_card.widget.dart';

class HomeScreen extends ConsumerStatefulWidget {
  const HomeScreen({super.key});

  @override
  ConsumerState<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends ConsumerState<HomeScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final ordersAsync = ref.watch(getOrdersFutureProvider);

    Widget buildTab(String text, {bool isSelected = false}) {
      return Tab(
        child: Container(
          decoration: isSelected
              ? null
              : BoxDecoration(
                  border:
                      Border.all(color: ColorManager.primaryColor, width: 1.5),
                  borderRadius: BorderRadius.circular(7.r),
                ),
          alignment: Alignment.center,
          child: Text(text),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        backgroundColor: ColorManager.white,
        surfaceTintColor: ColorManager.white,
        centerTitle: false,
        title: Text(
          context.tr.ahmadAli,
          style: AppTextStyles.title,
        ),
        leading: Padding(
          padding: const EdgeInsets.only(
            right: AppSpaces.padding8,
            bottom: AppSpaces.padding8,
            top: AppSpaces.padding8,
          ),
          child: CircleAvatar(
            backgroundColor: ColorManager.lightPrimaryColor,
            radius: 40.r,
            child: ClipOval(
              child: BaseCachedImage(
                height: 80.h,
                'https://cdn4.iconfinder.com/data/icons/gamer-player-3d-avatars-3d-illustration-pack/512/19_Man_T-Shirt_Suit.png',
                fit: BoxFit.cover,
              ),
            ),
          ),
        ),
        actions: [
          IconButton(
            onPressed: () {},
            icon: const Icon(Icons.notifications),
          ),
        ],
      ),
      body: Column(
        children: [
          // Active Shipments Header
          Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(
              horizontal: AppSpaces.screenPadding,
              vertical: 16.h,
            ),
            child: Text(
              context.tr.activeShipments,
              style: AppTextStyles.headlineSmall.copyWith(
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
          ),

          // Tab Bar
          TabBar(
            controller: _tabController,
            indicator: BoxDecoration(
              borderRadius: BorderRadius.circular(7.r),
              color: ColorManager.primaryColor,
            ),
            indicatorPadding: EdgeInsets.symmetric(horizontal: 16.w),
            indicatorSize: TabBarIndicatorSize.tab,
            labelColor: Colors.white,
            unselectedLabelColor: ColorManager.primaryColor,
            labelStyle: AppTextStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.w600,
              fontFamily: GoogleFonts.cairo().fontFamily,
            ),
            unselectedLabelStyle: AppTextStyles.bodyMedium.copyWith(
              fontFamily: GoogleFonts.cairo().fontFamily,
            ),
            dividerColor: Colors.transparent,
            tabs: [
              buildTab(context.tr.myReceivedShipments,
                  isSelected: _tabController.index == 0),
              buildTab(context.tr.mySentShipments,
                  isSelected: _tabController.index == 1),
            ],
          ),

          AppGaps.gap16,

          // Tab Bar View
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                // Received Shipments Tab
                ordersAsync.get(
                  data: (ordersResponse) => _buildShipmentsList(
                    ordersResponse.data.created,
                    context.tr.noReceivedShipments,
                  ),
                ),
                // Sent Shipments Tab
                ordersAsync.get(
                  data: (ordersResponse) => _buildShipmentsList(
                    ordersResponse.data.received,
                    context.tr.noSentShipments,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildShipmentsList(List<OrderModel> orders, String emptyMessage) {
    if (orders.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.inventory_2_outlined,
              size: 80.w,
              color: ColorManager.greyIcon,
            ),
            AppGaps.gap16,
            Text(
              emptyMessage,
              style: AppTextStyles.bodyLarge.copyWith(
                color: ColorManager.greyText,
              ),
            ),
          ],
        ),
      );
    }

    return BaseList<OrderModel>(
      data: orders,
      padding: EdgeInsets.only(bottom: 100.h),
      itemBuilder: (order, index) => ShipmentCardWidget(
        order: order,
        onTap: () {
          // TODO: Navigate to shipment details
        },
      ),
    );
  }
}
